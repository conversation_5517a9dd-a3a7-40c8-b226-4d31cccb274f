# ShopTunes 🎵

**Instantly bring Spotify music to your website!**

ShopTunes is a Wix app that allows website owners to seamlessly embed Spotify playlists on their Wix sites. With an intuitive dashboard interface, users can customize the appearance, position, and behavior of their music widgets to enhance their website's user experience.

## ✨ Features

- 🎵 **Spotify Integration**: Embed any public Spotify playlist directly on your website
- 🎨 **Customizable Themes**: Choose between light and dark themes to match your site
- 📐 **Flexible Positioning**: Place your music widget at the top, center, or bottom of your site
- 📏 **Adjustable Heights**: Standard, compact, or custom height options
- 🔧 **Easy Configuration**: User-friendly dashboard for managing all settings
- 🔄 **Real-time Preview**: See changes instantly before publishing
- 🚀 **One-click Enable/Disable**: Toggle your music widget without uninstalling

## 🛠️ Technology Stack

- **Frontend**: React 16.14.0 with TypeScript
- **Styling**: TailwindCSS 4.1.8 with PostCSS
- **Forms**: React Hook Form with Zod validation
- **UI Components**: Wix Design System
- **Build Tool**: Wix CLI
- **Package Manager**: npm/yarn

## 🚀 Quick Start

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn
- Wix CLI installed globally

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd development-shoptune
   ```

2. **Install dependencies**

   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

## 📋 Available Scripts

| Command             | Description                                 |
| ------------------- | ------------------------------------------- |
| `npm run dev`       | Start development server with hot reload    |
| `npm run build`     | Build the app for production                |
| `npm run release`   | Release the app to Wix App Market           |
| `npm run preview`   | Preview the built app locally               |
| `npm run generate`  | Generate new components/pages using Wix CLI |
| `npm run logs`      | View app logs                               |
| `npm run typecheck` | Run TypeScript type checking                |
| `npm run format`    | Format code using Prettier                  |

## 🔧 Configuration

### Wix App Configuration

The app is configured in `wix.config.json`:

```json
{
  "appId": "9127b689-e56f-4c34-a73c-79deb9ffb7a2",
  "projectId": "development-shoptune"
}
```

### Environment Setup

1. Ensure you have the Wix CLI installed:

   ```bash
   npm install -g @wix/cli
   ```

2. Login to your Wix account:
   ```bash
   wix login
   ```

## 📖 Usage

### For Developers

1. **Development Workflow**:

   - Run `npm run dev` to start the development server
   - Make changes to components in `src/dashboard/` or `src/site/`
   - Use `npm run typecheck` to verify TypeScript types
   - Format code with `npm run format`

2. **Building and Deployment**:
   - Build: `npm run build`
   - Preview: `npm run preview`
   - Release: `npm run release`

### For End Users

1. **Adding a Spotify Playlist**:

   - Go to [Spotify](https://open.spotify.com)
   - Select your favorite playlist
   - Click "..." on the playlist
   - Choose "Share" and "Copy link"
   - Paste the link in the "URL Spotify" field
   - Click "Embed"

2. **Customizing the Widget**:
   - Choose position: Top, Center, or Bottom of your site
   - Select height: Standard (352px), Compact, or Custom
   - Pick theme: Light or Dark
   - Enable/disable the widget as needed

## 📁 Project Structure

```
development-shoptune/
├── src/
│   ├── dashboard/           # Dashboard interface components
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Dashboard pages
│   │   ├── lib/            # Utility functions
│   │   └── styles/         # Global styles
│   └── site/               # Site-embedded scripts
│       └── embedded-scripts/ # Scripts that run on user sites
├── dist/                   # Built files
├── wix.config.json        # Wix app configuration
├── package.json           # Dependencies and scripts
└── tsconfig.json          # TypeScript configuration
```

## 🔍 Key Components

### Dashboard Components

- **BannerEnable**: Toggle widget on/off with helpful instructions
- **EmbedPlaylist**: Interface for adding Spotify playlist URLs with step-by-step guide
- **Customize**: Widget customization options (position, height, theme)
- **Preview**: Real-time preview of the Spotify embed

### Site Integration

- **ShopTunesEmbed**: Main class that handles Spotify API integration and widget rendering on user sites
- **Logger**: Comprehensive logging system for debugging and monitoring

## 🐛 Troubleshooting

### Common Issues

1. **Widget not showing on site**

   - Check if the widget is enabled in the dashboard
   - Verify the Spotify URL is valid and public
   - Ensure the site has finished loading before the widget initializes

2. **Invalid Spotify URL error**

   - Make sure you're using a public Spotify playlist/track/album URL
   - Copy the link directly from Spotify's share feature
   - Supported formats: `https://open.spotify.com/playlist/...`, `https://open.spotify.com/track/...`

3. **Development server issues**

   - Clear node_modules and reinstall: `rm -rf node_modules && npm install`
   - Check Node.js version (requires 14+)
   - Ensure Wix CLI is properly installed and authenticated

4. **Build failures**
   - Run `npm run typecheck` to identify TypeScript errors
   - Check for missing dependencies
   - Verify all environment variables are set

### Debug Mode

Enable detailed logging by checking the browser console for `[ShopTunes]` messages when the widget loads.

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **Make your changes**
   - Follow the existing code style
   - Add TypeScript types for new features
   - Update tests if applicable
4. **Format your code**
   ```bash
   npm run format
   ```
5. **Run type checking**
   ```bash
   npm run typecheck
   ```
6. **Commit your changes**
   ```bash
   git commit -m "feat: add your feature description"
   ```
7. **Push and create a Pull Request**

### Code Style Guidelines

- Use TypeScript for all new code
- Follow React functional component patterns
- Use Wix Design System components when possible
- Maintain consistent naming conventions
- Add proper error handling and logging

## 📚 Resources

- [Wix CLI Documentation](https://dev.wix.com/docs/build-apps/developer-tools/cli/get-started/about-the-wix-cli-for-apps)
- [Wix Design System](https://www.wix.com/design-system)
- [Spotify Web API](https://developer.spotify.com/documentation/web-api/)
- [React Hook Form](https://react-hook-form.com/)
- [Zod Validation](https://zod.dev/)

## 📄 License

This project was bootstrapped with [Create Wix App](https://www.npmjs.com/package/@wix/create-app).

## 📞 Support

For questions, issues, or feature requests:

- Create an issue in this repository
- Contact the development team
- Check the [Wix Developer Forum](https://dev.wix.com/forum) for general Wix app development questions

---

**Made with ❤️ for the Wix community**
