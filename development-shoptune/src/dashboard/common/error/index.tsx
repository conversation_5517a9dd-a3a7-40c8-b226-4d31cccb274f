import React, { <PERSON> } from "react";

interface ErrorProps {
  message?: string;
  details?: string;
  onRetry?: () => void;
}

const Error: FC<ErrorProps> = ({ message = "Something went wrong.", details, onRetry }) => {
  return (
    <div className='border border-red-500 bg-red-50 text-red-800 p-6 rounded-lg flex flex-col items-center max-w-md mx-auto mt-10 shadow-md'>
      <div className='text-5xl mb-3'>❌</div>
      <div className='font-semibold text-xl mb-2'>{message}</div>
      {details && <div className='mb-4 text-center'>{details}</div>}
      {onRetry && (
        <button
          onClick={onRetry}
          className='bg-red-500 hover:bg-red-600 text-white rounded px-4 py-2 font-medium transition-colors'
        >
          Retry
        </button>
      )}
    </div>
  );
};

export default Error;
