import React, { <PERSON> } from "react";
import { Box, Card, Text, Loader } from "@wix/design-system";

import { useRoot } from "../../hooks/useRoot";

const ShopInfo: FC = () => {
  const { isLoading, siteId, shopInfo, error } = useRoot();

  if (isLoading) {
    return (
      <Card>
        <Card.Header title="Shop Information" />
        <Card.Divider />
        <Card.Content>
          <Box display="flex" justifyContent="center" alignItems="center" height="100px">
            <Loader size="medium" />
          </Box>
        </Card.Content>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Card.Header title="Shop Information" />
        <Card.Divider />
        <Card.Content>
          <Box display="block">
            <Text skin="error">{error}</Text>
          </Box>
        </Card.Content>
      </Card>
    );
  }

  return (
    <Card>
      <Card.Header title="Shop Information" />
      <Card.Divider />
      <Card.Content>
        <Box display="block" gap="SP2">
          <Box marginBottom="SP2">
            <Text weight="bold">Site ID:</Text>
            <Text>{siteId || "Not available"}</Text>
          </Box>
          
          {shopInfo && (
            <>
              <Box marginBottom="SP2">
                <Text weight="bold">Shop Domain:</Text>
                <Text>{shopInfo.shop?.domain || "Not available"}</Text>
              </Box>
              
              <Box marginBottom="SP2">
                <Text weight="bold">Shop ID:</Text>
                <Text>{shopInfo.shop?.id || "Not available"}</Text>
              </Box>
              
              <Box marginBottom="SP2">
                <Text weight="bold">Shopify Plan:</Text>
                <Text>{shopInfo.shop?.shopifyPlan || "Not available"}</Text>
              </Box>
              
              <Box marginBottom="SP2">
                <Text weight="bold">Currency:</Text>
                <Text>{shopInfo.shop?.currency || "Not available"}</Text>
              </Box>
              
              <Box marginBottom="SP2">
                <Text weight="bold">Active:</Text>
                <Text>{shopInfo.shop?.active ? "Yes" : "No"}</Text>
              </Box>
              
              <Box marginBottom="SP2">
                <Text weight="bold">Token:</Text>
                <Text>{shopInfo.token ? "Available" : "Not available"}</Text>
              </Box>
            </>
          )}
        </Box>
      </Card.Content>
    </Card>
  );
};

export default ShopInfo;
