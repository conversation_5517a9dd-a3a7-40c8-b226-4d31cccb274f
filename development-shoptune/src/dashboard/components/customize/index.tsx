import React, { <PERSON> } from "react";
import { useFormContext } from "react-hook-form";

import DropdownControl from "@/dashboard/common/form/dropdown-control";
import InputControl from "@/dashboard/common/form/input-control";
import { Height } from "@/dashboard/enums/shoptunes.enum";
import { ShopTunesType } from "@/dashboard/pages/constant";
import { Box, Card } from "@wix/design-system";
import { optionsHeight, optionsPosition, optionsTheme } from "./constants";

const Customize: FC = () => {
  const { control, watch } = useFormContext<ShopTunesType>();
  const height = watch("height");

  return (
    <Card>
      <Card.Header title='Customize widget on your site' />
      <Card.Divider />
      <Card.Content>
        <Box display='block' className='space-y-3'>
          <DropdownControl<ShopTunesType>
            name='position'
            label='Position'
            placeholder='Select position'
            options={optionsPosition}
          />

          <DropdownControl<ShopTunesType>
            name='height'
            label='Height'
            placeholder='Select height'
            options={optionsHeight}
          />

          {height === Height.Custom && (
            <InputControl<ShopTunesType>
              name='customHeightValue'
              label='Custom Height (px)'
              placeholder='Enter height in pixels'
              type='number'
            />
          )}

          <DropdownControl<ShopTunesType>
            name='cardTheme'
            label='Theme'
            placeholder='Select theme'
            options={optionsTheme}
          />
        </Box>
      </Card.Content>
    </Card>
  );
};

export default Customize;
