import { CardTheme, Height, Position } from "@/dashboard/enums/shoptunes.enum";

export const optionsHeight = [
  { id: Height.Standard, value: "Standard (352px)" },
  { id: Height.Compact, value: "Compact (152px)" },
  { id: Height.Mini, value: "Mini (80px)" },
  { id: Height.Custom, value: "Custom" }
];

export const optionsPosition = [
  { id: Position.TopOfSite, value: "Top of site" },
  { id: Position.Center, value: "Center" },
  { id: Position.BottomOfSite, value: "Bottom of site" }
];

export const optionsTheme = [
  { id: CardTheme.Light, value: "Light" },
  { id: CardTheme.Dark, value: "Dark" }
];
