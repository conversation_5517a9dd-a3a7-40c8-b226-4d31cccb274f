import React, { FC, useState } from "react";
import { useFormContext } from "react-hook-form";

import { urlToSpotifyUri } from "@/dashboard/lib/utils";
import { ShopTunesType } from "@/dashboard/pages/constant";
import { Box, Button, Card, FormField, Input, Text } from "@wix/design-system";
import InstructionsSteps from "./instructions-steps";

const EmbedPlaylist: FC = () => {
  const [currentUrl, setCurrentUrl] = useState<string>("");
  const [error, setError] = useState<string>("");
  const { setValue } = useFormContext<ShopTunesType>();

  const handleEmbed = () => {
    if (!urlToSpotifyUri(currentUrl.trim())) {
      setError("Invalid Spotify URL");
      return;
    }
    setValue("spotifyLink", currentUrl);
    setError("");
  };

  return (
    <Card>
      <Card.Header title='Embed your playlist' />
      <Card.Divider />
      <Card.Content>
        <Box display='block'>
          <Box gap='4'>
            <Box display='block' flex='1'>
              <Text secondary>Paste URL Spotify</Text>
              <FormField
                {...(error && {
                  status: "error"
                })}
                statusMessage={error}
                classNames='mt-2'
              >
                <Input
                  placeholder='Paste here'
                  value={currentUrl}
                  onChange={(e) => setCurrentUrl(e.target.value)}
                />
              </FormField>
            </Box>
            <Button className='mt-8' onClick={handleEmbed}>
              Embed
            </Button>
          </Box>
          <InstructionsSteps />
        </Box>
      </Card.Content>
    </Card>
  );
};

export default EmbedPlaylist;
