import React, { FC, useEffect, useRef } from "react";
import { useFormContext } from "react-hook-form";

import { urlToSpotifyUri } from "@/dashboard/lib/utils";
import { ShopTunesType } from "@/dashboard/pages/constant";
import { Box, Card } from "@wix/design-system";

interface SpotifyEmbedController {
  loadUri: (uri: string) => void;
  addListener: (event: string, callback: (e: any) => void) => void;
}

interface SpotifyIframeApi {
  createController: (
    element: HTMLElement | null,
    options: { uri: string; width?: string; height?: string },
    callback: (controller: SpotifyEmbedController) => void
  ) => void;
}

declare global {
  interface Window {
    SpotifyIframeApi?: SpotifyIframeApi;
    onSpotifyIframeApiReady?: (api: SpotifyIframeApi) => void;
    EmbedController?: SpotifyEmbedController;
  }
}

const Preview: FC = () => {
  const { watch } = useFormContext<ShopTunesType>();
  const spotifyLink = watch("spotifyLink") || "";
  const height = watch("height");
  const customHeight = watch("customHeight");
  const cardTheme = watch("cardTheme");

  const controllerRef = useRef<SpotifyEmbedController | null>(null);
  const apiReadyRef = useRef(false);
  const lastHeightRef = useRef<number>(0);
  const lastThemeRef = useRef<string>("");

  // Memoize the height calculation to avoid unnecessary re-renders
  const calculatedHeight = React.useMemo((): number => {
    const heightValue = Number(height);
    switch (heightValue) {
      case 0:
        return 352; // Standard
      case 1:
        return 152; // Compact
      case 2:
        return 80; // Mini
      case 3:
        return Number(customHeight) || 352; // Custom
      default:
        return 352;
    }
  }, [height, customHeight]);

  useEffect(() => {
    if (document.querySelector('script[src="https://open.spotify.com/embed/iframe-api/v1"]')) {
      apiReadyRef.current = true;
      return;
    }

    const script = document.createElement("script");
    script.src = "https://open.spotify.com/embed/iframe-api/v1";
    script.async = true;
    document.head.appendChild(script);

    window.onSpotifyIframeApiReady = (IFrameAPI: SpotifyIframeApi) => {
      window.SpotifyIframeApi = IFrameAPI;
      apiReadyRef.current = true;
    };

    return () => {
      if (script.parentNode) {
        document.head.removeChild(script);
      }
    };
  }, []);

  useEffect(() => {
    const element = document.getElementById("embed-iframe");
    if (!element) return;

    if (!spotifyLink) {
      element.innerHTML = `
				<div style="
					display: flex;
					align-items: center;
					justify-content: center;
					height: ${calculatedHeight}px;
					border: 2px dashed #ccc;
					border-radius: 12px;
					color: #666;
					font-family: Arial, sans-serif;
					box-shadow: 0 4px 12px rgba(0,0,0,0.1);
				">
					<div style="text-align: center;">
						<div style="font-size: 24px; margin-bottom: 12px;">🎵</div>
						<div style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">No playlist selected</div>
						<div style="font-size: 12px; opacity: 0.8;">Add a Spotify playlist to see preview</div>
					</div>
				</div>
			`;
      lastHeightRef.current = 0;
      lastThemeRef.current = "";
      return;
    }

    const heightChanged = lastHeightRef.current !== calculatedHeight;
    const themeChanged = lastThemeRef.current !== (cardTheme || "light");
    const needsRecreation = heightChanged || themeChanged || !controllerRef.current;

    if (controllerRef.current && !needsRecreation) {
      const uri = urlToSpotifyUri(spotifyLink);
      if (uri) {
        try {
          controllerRef.current.loadUri(uri);
          return;
        } catch (error) {}
      }
    }

    // Clear existing iframe content and reset controller
    element.innerHTML = "";
    controllerRef.current = null;

    const iframeContainer = document.createElement("div");
    iframeContainer.id = "spotify-embed-container";
    iframeContainer.style.cssText = `
			border-radius: 8px;
			overflow: hidden;
			height: ${calculatedHeight}px;
		`;

    element.appendChild(iframeContainer);

    // Function to create the controller
    const createController = () => {
      if (!window.SpotifyIframeApi) {
        return;
      }

      const uri = urlToSpotifyUri(spotifyLink);
      console.log(" createController ~ uri:", uri);
      if (!uri) {
        console.log("Invalid Spotify URI");
        return;
      }

      const options = {
        width: "100%",
        height: calculatedHeight.toString(),
        uri: uri,
        theme: cardTheme
      };

      const callback = (EmbedController: SpotifyEmbedController) => {
        controllerRef.current = EmbedController;
        window.EmbedController = EmbedController;

        lastHeightRef.current = calculatedHeight;
        lastThemeRef.current = cardTheme || "light";

        // EmbedController.addListener("playback_started", (e: any) => {
        // 	console.log("Playback started for:", e.data.playingURI);
        // });
      };

      window.SpotifyIframeApi.createController(iframeContainer, options, callback);
    };

    if (window.SpotifyIframeApi) {
      createController();
    } else {
      let timeoutId: number;
      const checkApiReady = () => {
        if (window.SpotifyIframeApi) {
          createController();
        } else {
          timeoutId = setTimeout(checkApiReady, 100);
        }
      };
      checkApiReady();

      return () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      };
    }
  }, [spotifyLink, calculatedHeight, cardTheme]);

  return (
    <Card>
      <Card.Header title='Preview' />
      <Card.Divider />
      <Card.Content>
        <Box display='block'>
          <div id='embed-iframe' className='embed-iframe' style={{ minHeight: 80 }} />
        </Box>
      </Card.Content>
    </Card>
  );
};

export default Preview;
