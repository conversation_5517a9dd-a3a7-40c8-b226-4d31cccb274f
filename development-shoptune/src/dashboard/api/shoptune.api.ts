import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../lib/api-handler";
import { passParams } from "../lib/pass-params";
import { ShoptunesRequestModel } from "../models/shoptunes/request.model";
import { ShoptunesResponseModel } from "../models/shoptunes/response.model";

export class ShoptunesApi {
  static async GetShoptunes() {
    const response = await <PERSON><PERSON>Handler<ShoptunesResponseModel>({
      method: "GET",
      url: passParams("/shoptunes")
    });
    return response.result as ShoptunesResponseModel;
  }
  static async UpdateShoptunes(body: ShoptunesRequestModel) {
    const response = await <PERSON><PERSON><PERSON>and<PERSON><unknown>({
      method: "POST",
      url: passParams("/shoptunes/update"),
      body
    });
    return response;
  }
}
