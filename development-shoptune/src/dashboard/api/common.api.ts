import { <PERSON>pi<PERSON>and<PERSON> } from "../lib/api-handler";
import { passParams } from "../lib/pass-params";
import { ShopInfoDto } from "../models/common/shop-data.model";

export class CommonApi {
  static async GetShop(siteId: string) {
    const response = await ApiHandler<ShopInfoDto>({
      method: "GET",
      url: passParams("/common/shop-info", { siteId })
    });
    return response.result as ShopInfoDto;
  }
}
