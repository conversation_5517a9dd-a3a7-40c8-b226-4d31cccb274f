import { z } from "zod";

import { CardTheme, Height, Position } from "@/dashboard/enums/shoptunes.enum";
import { shopTunesSchema } from "./schema";

export type ShopTunesType = z.infer<typeof shopTunesSchema>;

export const shopTunesDefault: ShopTunesType = {
  enableWidget: true,
  spotifyLink: "",
  position: Position.TopOfSite,
  height: Height.Standard,
  customHeightValue: 0,
  cardTheme: CardTheme.Light
};
