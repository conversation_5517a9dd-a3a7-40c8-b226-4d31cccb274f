import { z } from "zod";

import { CardTheme, Height, Position } from "@/dashboard/enums/shoptunes.enum";
import { urlToSpotifyUri } from "../lib/utils";

export const shopTunesSchema = z.object({
  enableWidget: z.boolean(),
  spotifyLink: z.string().refine(
    (url) => {
      if (!url) return true;
      try {
        new URL(url);
        return urlToSpotifyUri(url);
      } catch {
        return false;
      }
    },
    {
      message: "Invalid Spotify URL"
    }
  ),
  position: z.nativeEnum(Position),
  height: z.nativeEnum(Height),
  customHeightValue: z.string().or(z.number()),
  cardTheme: z.nativeEnum(CardTheme)
});
