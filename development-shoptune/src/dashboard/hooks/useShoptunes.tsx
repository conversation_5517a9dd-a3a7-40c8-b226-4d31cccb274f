import React, { ReactNode, createContext, useContext, useEffect, useState } from "react";

import { ShoptunesApi } from "../api/shoptune.api";
import { ShoptunesResponseModel } from "../models/shoptunes/response.model";

interface ShoptunesProps {
  shoptunes: ShoptunesResponseModel | null;
  isLoading: boolean;
}
const ShoptunesContext = createContext({} as ShoptunesProps);

const ShoptunesProvider = ({ children }: { children: ReactNode }) => {
  const [shoptunes, setShoptunes] = useState<ShoptunesResponseModel | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchShoptunes = async () => {
      try {
        setIsLoading(true);
        const shoptunes = await ShoptunesApi.GetShoptunes();
        if (shoptunes) {
          setShoptunes(shoptunes);
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchShoptunes();
  }, []);

  return (
    <ShoptunesContext.Provider value={{ shoptunes, isLoading }}>
      {children}
    </ShoptunesContext.Provider>
  );
};

export default ShoptunesProvider;

export const useShoptunes = () => {
  const context = useContext(ShoptunesContext);
  if (!context) {
    throw new Error("useShoptunes must be used within a ShoptunesProvider");
  }
  return context;
};
