import { dashboard } from "@wix/dashboard";
import React, { ReactNode, createContext, useContext, useEffect, useState } from "react";

import { CommonApi } from "../api/common.api";
import { ShopInfoDto } from "../models/common/shop-data.model";

interface RootProps {
  isLoading: boolean;
  siteId: string | null;
  shopInfo: ShopInfoDto | null;
  error: string | null;
}

const RootContext = createContext({} as RootProps);

const RootProvider = ({ children }: { children: ReactNode }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [siteId, setSiteId] = useState<string | null>(null);
  const [shopInfo, setShopInfo] = useState<ShopInfoDto | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeRoot = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Use dashboard.observeState to get contextual information
        dashboard.observeState((_componentParams, _environmentState) => {
          console.log("🚀 useRoot.tsx:30 - _environmentState:", _environmentState);

          console.log("🚀 useRoot.tsx:30 - _componentParams:", _componentParams);

          // Extract siteId from the URL
          // In Wix dashboard, siteId appears after /dashboard/ in the URL
          const currentUrl = window.location.href;
          console.log("🚀 useRoot.tsx:33 - currentUrl:", currentUrl);

          const siteIdMatch = currentUrl.match(/\/dashboard\/([a-f0-9-]+)/);
          console.log("🚀 useRoot.tsx:36 - siteIdMatch:", siteIdMatch);

          if (siteIdMatch && siteIdMatch[1]) {
            const extractedSiteId = siteIdMatch[1];
            console.log("🚀 useRoot.tsx:37 - extractedSiteId:", extractedSiteId);

            setSiteId(extractedSiteId);

            // Call CommonApi.GetShop with the extracted siteId
            fetchShopInfo(extractedSiteId);
          } else {
            setError("Could not extract siteId from URL");
            setIsLoading(false);
          }
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to initialize root context");
        setIsLoading(false);
      }
    };

    const fetchShopInfo = async (siteId: string) => {
      try {
        const shopData = await CommonApi.GetShop(siteId);
        setShopInfo(shopData);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch shop information");
      } finally {
        setIsLoading(false);
      }
    };

    initializeRoot();
  }, []);

  const value: RootProps = {
    isLoading,
    siteId,
    shopInfo,
    error
  };

  return <RootContext.Provider value={value}>{children}</RootContext.Provider>;
};

export default RootProvider;

export const useRoot = () => {
  const context = useContext(RootContext);
  if (!context) {
    throw new Error("useRoot must be used within a RootProvider");
  }
  return context;
};
