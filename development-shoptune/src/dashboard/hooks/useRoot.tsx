import React, { ReactN<PERSON>, createContext, useContext } from "react";

interface RootProps {
  isLoading: boolean;
  siteId: string | null;
}
const RootContext = createContext({} as RootProps);

const RootProvider = ({ children }: { children: ReactNode }) => {
  return <RootContext.Provider value={{}}>{children}</RootContext.Provider>;
};

export default RootProvider;

export const useRoot = () => {
  const context = useContext(RootContext);
  if (!context) {
    throw new Error("useRoot must be used within a RootProvider");
  }
  return context;
};
